#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双均线策略增强版 - 时间对齐修复版
金叉后第二天买入，死叉后第二天卖出
增加收益率曲线绘制功能，修复时间对齐问题
"""

import pandas as pd
import numpy as np
import os
import glob
import random
import pickle
import hashlib
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

class MovingAverageStrategyEnhanced:
    def __init__(self, short_window=5, long_window=20, data_dir="../日线数据", execution_price='open'):
        """
        初始化双均线策略

        Args:
            short_window: 短期均线周期，默认5天
            long_window: 长期均线周期，默认20天
            data_dir: 数据目录路径
            execution_price: 执行价格类型，可选 'open'(开盘价), 'close'(收盘价), 'random'(随机价)
        """
        self.short_window = short_window
        self.long_window = long_window
        self.data_dir = data_dir
        self.execution_price = execution_price
        self.results = {}
        self.detailed_results = {}  # 存储详细的收益率曲线数据

        # 缓存相关设置 - 修改为每个测算创建独立目录
        self.cache_base_dir = "./cache"
        from 策略配置 import EXECUTION_PRICE_TYPES
        price_suffix = EXECUTION_PRICE_TYPES.get(execution_price, {}).get('suffix', execution_price)

        # 为每个策略参数组合创建独立的缓存目录
        strategy_name = f"MA{short_window}_{long_window}_{price_suffix}"
        self.cache_dir = os.path.join(self.cache_base_dir, strategy_name)
        os.makedirs(self.cache_dir, exist_ok=True)

        # 保留旧的缓存文件路径用于兼容性检查
        self.legacy_cache_file = os.path.join(self.cache_base_dir, f"strategy_cache_MA{short_window}_{long_window}_{price_suffix}.pkl")

    def load_stock_data(self, file_path):
        """
        加载单个股票数据，完全避免列名冲突
        """
        try:
            # 直接读取CSV文件，不做任何列名处理
            df = pd.read_csv(file_path, encoding='gbk')

            if df.empty:
                return None

            # 将列名转为小写，但不改变列数
            original_columns = df.columns.tolist()
            lowercase_mapping = {}
            for col in original_columns:
                lowercase_mapping[col] = str(col).lower().strip()

            df = df.rename(columns=lowercase_mapping)

            # 获取当前所有列名
            current_cols = df.columns.tolist()

            # 检查并创建字段映射
            field_mapping = {}

            # 寻找日期字段
            date_candidates = ['date', 'time', '日期']
            date_col = None
            for candidate in date_candidates:
                if candidate in current_cols:
                    date_col = candidate
                    field_mapping['date'] = candidate
                    break

            if not date_col:
                return None

            # 寻找价格字段
            price_fields = {
                'open': ['open', 'opening', '开盘', '开盘价'],
                'high': ['high', 'highest', '最高', '最高价'],
                'low': ['low', 'lowest', '最低', '最低价'],
                'close': ['close', 'closing', '收盘', '收盘价'],
                'volume': ['volume', 'vol', '成交量', '量']
            }

            for field, candidates in price_fields.items():
                found = False
                for candidate in candidates:
                    if candidate in current_cols:
                        field_mapping[field] = candidate
                        found = True
                        break
                if not found:
                    return None

            # 寻找成交额字段
            turnover_candidates = ['turnover', 'amount', '成交额', '金额']
            for candidate in turnover_candidates:
                if candidate in current_cols:
                    field_mapping['turnover'] = candidate
                    break

            # 只保留需要的字段
            keep_cols = list(field_mapping.values())
            df = df[keep_cols].copy()

            # 重命名为标准字段名
            reverse_mapping = {v: k for k, v in field_mapping.items()}
            df = df.rename(columns=reverse_mapping)

            # 转换数据类型
            try:
                df['date'] = pd.to_datetime(df['date'])
            except:
                return None

            # 只保留2000年1月1日之后的数据
            df = df[df['date'] >= '2000-01-01']

            if df.empty:
                return None

            # 转换数值字段
            numeric_cols = ['open', 'high', 'low', 'close', 'volume']
            if 'turnover' in df.columns:
                numeric_cols.append('turnover')

            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 按日期排序并清理
            df = df.sort_values('date').reset_index(drop=True)
            df = df.dropna()

            if df.empty:
                return None

            return df

        except Exception as e:
            print(f"加载文件 {file_path} 时出错: {str(e)[:50]}...")
            return None

    def calculate_signals(self, df):
        """
        计算买卖信号

        Args:
            df: 股票数据DataFrame

        Returns:
            DataFrame: 添加了信号的数据
        """
        # 计算移动平均线
        df[f'MA{self.short_window}'] = df['close'].rolling(window=self.short_window).mean()
        df[f'MA{self.long_window}'] = df['close'].rolling(window=self.long_window).mean()

        # 计算信号
        df['signal'] = 0
        df['position'] = 0

        # 当短期均线上穿长期均线时为金叉，设置买入信号
        # 当短期均线下穿长期均线时为死叉，设置卖出信号
        for i in range(1, len(df)):
            if (df.iloc[i][f'MA{self.short_window}'] > df.iloc[i][f'MA{self.long_window}'] and
                df.iloc[i-1][f'MA{self.short_window}'] <= df.iloc[i-1][f'MA{self.long_window}']):
                df.loc[i, 'signal'] = 1  # 金叉买入信号
            elif (df.iloc[i][f'MA{self.short_window}'] < df.iloc[i][f'MA{self.long_window}'] and
                  df.iloc[i-1][f'MA{self.short_window}'] >= df.iloc[i-1][f'MA{self.long_window}']):
                df.loc[i, 'signal'] = -1  # 死叉卖出信号

        return df

    def get_execution_price(self, df, index):
        """
        根据执行价格类型获取执行价格

        Args:
            df: 股票数据DataFrame
            index: 数据索引

        Returns:
            float: 执行价格
        """
        if self.execution_price == 'open':
            return df.iloc[index]['open']
        elif self.execution_price == 'close':
            return df.iloc[index]['close']
        elif self.execution_price == 'random':
            # 在最高价和最低价之间随机选择
            high = df.iloc[index]['high']
            low = df.iloc[index]['low']
            return random.uniform(low, high)
        else:
            # 默认使用开盘价
            return df.iloc[index]['open']

    def backtest_strategy(self, df, stock_code):
        """
        增强版回测策略 - 包含停牌和一字涨跌停处理

        Args:
            df: 包含信号的股票数据
            stock_code: 股票代码

        Returns:
            dict: 回测结果
        """
        from 增强交易逻辑 import EnhancedTradingLogic

        # 初始化交易逻辑
        trading_logic = EnhancedTradingLogic()

        position = 0  # 持仓状态：0=空仓，1=持有
        buy_price = 0
        trades = []
        total_return = 0

        # 创建收益率曲线数据
        equity_curve = []
        cumulative_return = 0

        # 延期交易队列
        pending_buy_signal = False
        pending_sell_signal = False

        for i in range(1, len(df)):
            current_date = df.iloc[i]['date']
            current_price = df.iloc[i]['close']
            signal = df.iloc[i-1]['signal']  # 使用前一天的信号，模拟第二天执行

            # 检查是否有新的交易信号
            if signal == 1 and position == 0:
                pending_buy_signal = True
            elif signal == -1 and position == 1:
                pending_sell_signal = True

            # 处理买入信号
            if pending_buy_signal and position == 0:
                if trading_logic.can_buy(df, i):
                    # 可以买入
                    position = 1
                    buy_price = self.get_execution_price(df, i)
                    pending_buy_signal = False

                    trades.append({
                        'date': current_date,
                        'action': 'buy',
                        'price': buy_price,
                        'return': 0,
                        'cumulative_return': cumulative_return,
                        'execution_type': self.execution_price,
                        'delay_reason': None
                    })

                    # print(f"  {stock_code}: 买入 {buy_price:.2f} ({current_date.strftime('%Y-%m-%d')})")

                else:
                    # 不能买入，记录原因（可选择是否打印详细信息）
                    if trading_logic.is_suspended(df, i):
                        reason = "停牌"
                    elif trading_logic.is_limit_up(df, i):
                        reason = "一字涨停"
                    else:
                        reason = "其他"

                    # print(f"  {stock_code}: 延期买入 - {reason} ({current_date.strftime('%Y-%m-%d')})")

            # 处理卖出信号
            if pending_sell_signal and position == 1:
                if trading_logic.can_sell(df, i):
                    # 可以卖出
                    position = 0
                    sell_price = self.get_execution_price(df, i)
                    pending_sell_signal = False

                    # 防止除零错误
                    if buy_price > 0:
                        trade_return = (sell_price - buy_price) / buy_price
                        total_return += trade_return
                        cumulative_return = total_return
                    else:
                        trade_return = 0
                        print(f"  警告: {stock_code} 买入价格为0，跳过该交易")

                    trades.append({
                        'date': current_date,
                        'action': 'sell',
                        'price': sell_price,
                        'return': trade_return,
                        'cumulative_return': cumulative_return,
                        'execution_type': self.execution_price,
                        'delay_reason': None
                    })

                    # print(f"  {stock_code}: 卖出 {sell_price:.2f}, 收益率 {trade_return:.2%} ({current_date.strftime('%Y-%m-%d')})")

                else:
                    # 不能卖出，记录原因（可选择是否打印详细信息）
                    if trading_logic.is_suspended(df, i):
                        reason = "停牌"
                    elif trading_logic.is_limit_down(df, i):
                        reason = "一字跌停"
                    else:
                        reason = "其他"

                    # print(f"  {stock_code}: 延期卖出 - {reason} ({current_date.strftime('%Y-%m-%d')})")

            # 记录每日收益率曲线数据
            if position == 1:
                # 持仓期间的浮动收益
                if buy_price > 0:
                    floating_return = (current_price - buy_price) / buy_price
                    current_cumulative = cumulative_return + floating_return
                else:
                    current_cumulative = cumulative_return
            else:
                # 空仓期间保持之前的累计收益
                current_cumulative = cumulative_return

            equity_curve.append({
                'date': current_date,
                'price': current_price,
                'position': position,
                'cumulative_return': current_cumulative,
                'ma_short': df.iloc[i][f'MA{self.short_window}'],
                'ma_long': df.iloc[i][f'MA{self.long_window}'],
                'signal': df.iloc[i]['signal'],
                'suspended': trading_logic.is_suspended(df, i),
                'limit_up': trading_logic.is_limit_up(df, i),
                'limit_down': trading_logic.is_limit_down(df, i)
            })

        # 如果最后还持有股票，尝试卖出
        if position == 1:
            final_index = len(df) - 1
            if trading_logic.can_sell(df, final_index):
                final_price = self.get_execution_price(df, final_index)
                # 防止除零错误
                if buy_price > 0:
                    final_return = (final_price - buy_price) / buy_price
                    total_return += final_return
                else:
                    final_return = 0
                    print(f"  警告: {stock_code} 最终卖出时买入价格为0")

                trades.append({
                    'date': df.iloc[-1]['date'],
                    'action': 'sell',
                    'price': final_price,
                    'return': final_return,
                    'cumulative_return': total_return,
                    'execution_type': self.execution_price,
                    'delay_reason': None
                })
            else:
                # print(f"  {stock_code}: 最终持仓无法卖出（停牌或跌停）")
                pass

        return {
            'stock_code': stock_code,
            'total_return': total_return,
            'trade_count': len([t for t in trades if t['action'] == 'sell']),
            'trades': trades,
            'equity_curve': equity_curve
        }

    def plot_average_return_curve(self, stock_count=50, save_charts=True):
        """
        从已计算的缓存数据中随机选择指定数量的股票，计算并绘制时间对齐的平均收益率曲线，并添加上证指数对比

        Args:
            stock_count: 随机选择的股票数量，将从self.detailed_results(缓存)中选择
            save_charts: 是否保存图表

        Note:
            此方法只会从已经完成回测的股票(缓存中的数据)中选择，确保所有选中的股票都有完整的收益率曲线数据
        """
        if not self.detailed_results:
            print("没有可用的数据来计算平均收益率")
            return

        # 加载上证指数数据
        try:
            # 尝试使用和股票数据相同的load_stock_data方法
            index_file_path = os.path.join(self.data_dir, "SH000001.txt")
            index_data = self.load_stock_data(index_file_path)

            if index_data is not None:
                # 计算指数收益率
                index_data['return'] = index_data['close'].pct_change()
                index_data['cumulative_return'] = (1 + index_data['return']).cumprod() - 1
                print("成功加载上证指数数据")
            else:
                print("上证指数数据加载失败：数据为空或格式不支持")
                index_data = None
        except Exception as e:
            print(f"加载上证指数数据失败: {str(e)}")
            index_data = None

        # 随机选择股票（从缓存中）
        available_stocks = list(self.detailed_results.keys())
        if len(available_stocks) < stock_count:
            print(f"缓存中可用股票数量({len(available_stocks)})少于请求数量({stock_count})，使用所有缓存中的股票")
            selected_stocks = available_stocks
        else:
            selected_stocks = random.sample(available_stocks, stock_count)

        print(f"从缓存中随机选择了{len(selected_stocks)}只股票计算平均收益率曲线")
        print(f"选中的股票: {', '.join(selected_stocks[:10])}{'...' if len(selected_stocks) > 10 else ''}")

        # 收集所有选中股票的收益率曲线数据
        all_curves = []
        for stock_code in selected_stocks:
            result = self.detailed_results[stock_code]
            equity_data = pd.DataFrame(result['equity_curve'])
            equity_data['stock_code'] = stock_code
            all_curves.append(equity_data)

        if not all_curves:
            print("没有有效的收益率数据")
            return

        # 改进的时间对齐处理
        print("正在进行时间对齐处理...")
        # 获取所有日期的并集
        all_dates = set()
        for equity_data in all_curves:
            all_dates.update(equity_data['date'])
        all_dates = sorted(list(all_dates))

        print(f"日期范围: {all_dates[0].strftime('%Y-%m-%d')} 到 {all_dates[-1].strftime('%Y-%m-%d')}")
        print(f"总交易日数量: {len(all_dates)}")

        # 为每只股票创建完整的日期序列，缺失日期用前值填充
        aligned_data = []
        valid_stocks = []

        for i, equity_data in enumerate(all_curves):
            stock_code = selected_stocks[i]

            # 创建完整日期的DataFrame
            full_dates_df = pd.DataFrame({'date': all_dates})

            # 合并数据，缺失值使用前值填充
            aligned_stock = pd.merge(full_dates_df, equity_data, on='date', how='left')
            aligned_stock = aligned_stock.sort_values('date')

            # 使用新的pandas语法进行前向填充
            aligned_stock['cumulative_return'] = aligned_stock['cumulative_return'].ffill()
            aligned_stock['price'] = aligned_stock['price'].ffill()
            aligned_stock['position'] = aligned_stock['position'].fillna(0)

            # 如果起始值仍为空，用0填充（股票上市前的数据）
            aligned_stock['cumulative_return'] = aligned_stock['cumulative_return'].fillna(0)
            aligned_stock['price'] = aligned_stock['price'].fillna(0)

            # 检查数据有效性和覆盖率
            total_days = len(aligned_stock)
            valid_days = aligned_stock['cumulative_return'].notna().sum()
            coverage_ratio = valid_days / total_days

            # 只保留有足够数据覆盖率的股票
            if coverage_ratio > 0.1:  # 至少10%的数据有效
                aligned_stock['stock_code'] = stock_code
                aligned_data.append(aligned_stock)
                valid_stocks.append(stock_code)
                print(f"  {stock_code}: 数据覆盖率 {coverage_ratio:.1%} ({valid_days}/{total_days}天)")
            else:
                print(f"  警告: {stock_code} 数据覆盖率不足 {coverage_ratio:.1%}，已排除")

        if len(aligned_data) < 3:
            print("警告: 有效股票数量太少，无法生成可靠的平均收益率曲线")
            return None, None

        print(f"时间对齐完成，有效股票数量: {len(aligned_data)}")

        # 合并对齐后的数据
        combined_data = pd.concat(aligned_data, ignore_index=True)

        # 验证时间对齐效果
        date_counts = combined_data.groupby('date').size()
        expected_count = len(aligned_data)
        perfect_alignment = (date_counts == expected_count).all()

        print(f"时间对齐验证: {'✅ 完美对齐' if perfect_alignment else '⚠️ 部分对齐'}")
        print(f"预期每日样本数: {expected_count}, 实际范围: {date_counts.min()}-{date_counts.max()}")

        # 按日期分组计算平均收益率（现在所有股票在每个日期都有数据）
        daily_avg = combined_data.groupby('date').agg({
            'cumulative_return': ['mean', 'std', 'min', 'max', 'count'],
            'price': 'mean',
            'position': 'mean'
        }).reset_index()

        # 重命名列
        daily_avg.columns = ['date', 'avg_return', 'std_return', 'min_return', 'max_return', 'sample_count', 'avg_price', 'avg_position']

        # 打印样本数量信息
        print(f"每日样本数量: 最小{daily_avg['sample_count'].min()}, 最大{daily_avg['sample_count'].max()}, 平均{daily_avg['sample_count'].mean():.1f}")

        # 更新选中的股票列表为实际有效的股票
        selected_stocks = valid_stocks

        # 计算最终收益率
        final_returns = []
        for stock_code in selected_stocks:
            result = self.detailed_results[stock_code]
            final_returns.append(result['total_return'] * 100)  # 转换为百分比

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), height_ratios=[3, 1])

        # 第一个子图：平均收益率曲线
        ax1.plot(daily_avg['date'], daily_avg['avg_return'] * 100,
                label='策略平均收益率', linewidth=2, color='blue')

        # 添加上证指数对比
        if index_data is not None:
            # 对齐指数数据日期
            index_aligned = pd.merge(daily_avg[['date']], index_data[['date', 'cumulative_return']],
                                   on='date', how='left')
            index_aligned['cumulative_return'] = index_aligned['cumulative_return'].ffill()

            # 绘制指数曲线，使用更醒目的样式
            ax1.plot(index_aligned['date'], index_aligned['cumulative_return'] * 100,
                    label='上证指数收益率',
                    linewidth=3,  # 增加线条粗细
                    color='#FF4500',  # 使用更醒目的橙红色
                    alpha=0.9,  # 增加不透明度
                    marker='.',  # 添加标记点
                    markersize=2,  # 标记点大小
                    markevery=30)  # 每30个点显示一个标记

            # 添加指数数据的背景色
            ax1.fill_between(index_aligned['date'],
                           index_aligned['cumulative_return'] * 100,
                           alpha=0.1,  # 设置透明度
                           color='#FF4500',  # 使用相同的颜色
                           label='上证指数区域')

            # 在图表右上角添加指数最终收益率标注
            index_final_return = index_aligned['cumulative_return'].iloc[-1] * 100
            ax1.annotate(f'上证指数: {index_final_return:.1f}%',
                        xy=(1, 1), xycoords='axes fraction',
                        xytext=(-10, -10), textcoords='offset points',
                        ha='right', va='top',
                        bbox=dict(boxstyle='round,pad=0.5', fc='#FF4500', alpha=0.2),
                        color='#FF4500', fontweight='bold')

        # 添加标准差区间
        ax1.fill_between(daily_avg['date'],
                        (daily_avg['avg_return'] - daily_avg['std_return']) * 100,
                        (daily_avg['avg_return'] + daily_avg['std_return']) * 100,
                        alpha=0.2, color='blue', label='标准差区间')

        ax1.set_title(f'随机{len(selected_stocks)}只股票的平均收益率曲线 (MA{self.short_window}-MA{self.long_window})')
        ax1.set_ylabel('收益率 (%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 第二个子图：样本数量
        ax2.plot(daily_avg['date'], daily_avg['sample_count'],
                label='每日样本数量', color='green', linewidth=1)
        ax2.set_xlabel('日期')
        ax2.set_ylabel('样本数量')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 设置日期格式
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 显示统计信息
        print(f"\n平均收益率统计:")
        print(f"样本数量: {len(selected_stocks)}")
        print(f"平均最终收益率: {np.mean(final_returns):.2f}%")
        print(f"中位数收益率: {np.median(final_returns):.2f}%")
        print(f"收益率标准差: {np.std(final_returns):.2f}%")
        print(f"最大收益率: {np.max(final_returns):.2f}%")
        print(f"最小收益率: {np.min(final_returns):.2f}%")
        print(f"盈利股票比例: {len([r for r in final_returns if r > 0])/len(final_returns):.1%}")

        if save_charts:
            chart_dir = f"收益率曲线图_MA{self.short_window}-{self.long_window}"
            os.makedirs(chart_dir, exist_ok=True)
            filename = os.path.join(chart_dir, f"时间对齐_随机{len(selected_stocks)}只股票平均收益率曲线_含指数对比.png")
            plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"\n时间对齐的平均收益率曲线图已保存: {filename}")

        plt.show()

        return selected_stocks, daily_avg

    def run_strategy(self, generate_charts=True, use_cache=True, save_interval=20):
        """
        运行策略，对所有股票进行回测

        Args:
            generate_charts: 是否生成图表
            use_cache: 是否使用缓存功能
            save_interval: 进度显示间隔，每处理多少只股票显示一次进度（实时缓存已启用）
        """
        print(f"开始运行双均线策略 (短期: {self.short_window}天, 长期: {self.long_window}天)")
        if use_cache:
            print(f"💾 实时缓存：每个股票处理完成后立即保存，每 {save_interval} 只股票显示一次进度")
        print("=" * 60)

        if use_cache:
            # 加载缓存 - 使用新的单个股票缓存机制
            cached_stocks, files_to_process = self.load_individual_cache()
            total_cached = len(cached_stocks)
        else:
            # 不使用缓存，处理所有文件
            files_to_process = glob.glob(os.path.join(self.data_dir, "*.txt"))
            total_cached = 0
            print("🚫 缓存功能已禁用，将重新计算所有股票")

        if not files_to_process and total_cached == 0:
            print(f"在目录 {self.data_dir} 中未找到股票数据文件")
            return

        total_returns = []
        successful_backtests = len(self.results)  # 包括缓存的结果

        # 处理需要计算的文件
        if files_to_process:
            print(f"\n🔄 开始处理 {len(files_to_process)} 只股票...")
            processed_count = 0  # 本次处理的股票计数

            for i, file_path in enumerate(files_to_process, 1):
                stock_code = os.path.basename(file_path).replace('.txt', '')

                print(f"正在处理股票 ({i}/{len(files_to_process)}): {stock_code}")

                # 加载数据
                df = self.load_stock_data(file_path)
                if df is None or len(df) < self.long_window:
                    print(f"  跳过 {stock_code}: 数据不足或加载失败")
                    continue

                # 计算信号
                df = self.calculate_signals(df)

                # 回测
                result = self.backtest_strategy(df, stock_code)

                if result['trade_count'] > 0:
                    self.results[stock_code] = result
                    self.detailed_results[stock_code] = result  # 保存详细结果
                    successful_backtests += 1
                    processed_count += 1

                    print(f"  {stock_code}: 总收益率 {result['total_return']:.2%}, 交易次数 {result['trade_count']}")

                    # 立即保存单个股票的缓存
                    if use_cache:
                        self.save_single_stock_cache(stock_code, result)
                else:
                    print(f"  {stock_code}: 无交易信号")

                # 显示进度信息（实时缓存已启用，此处仅显示进度）
                if processed_count % save_interval == 0:
                    print(f"\n📊 进度更新: 已处理 {processed_count} 只股票")
                    print(f"   当前进度: {i}/{len(files_to_process)} ({i/len(files_to_process)*100:.1f}%)")
                    print(f"   已实时缓存股票数: {len(self.results)}")

            # 处理完成提示
            if use_cache and processed_count > 0:
                print(f"\n✅ 处理完成，所有股票已实时缓存")
                print(f"   总计缓存了 {len(self.results)} 只股票的结果")
        else:
            print("✅ 所有股票都已缓存，无需重新计算")

        # 收集所有收益率（包括缓存的）
        for result in self.results.values():
            total_returns.append(result['total_return'])

        # 计算并显示总结果
        total_stocks = len(glob.glob(os.path.join(self.data_dir, "*.txt")))
        print(f"\n📈 处理完成:")
        print(f"   新计算: {len(files_to_process) if files_to_process else 0} 只股票")
        print(f"   使用缓存: {total_cached} 只股票")
        print(f"   总计: {total_stocks} 只股票")

        self.display_summary(total_returns, successful_backtests, total_stocks)

    def display_summary(self, total_returns, successful_backtests, total_stocks):
        """
        显示策略总结果
        """
        print("\n" + "=" * 60)
        print("策略回测总结")
        print("=" * 60)

        if not total_returns:
            print("没有成功的回测结果")
            return

        avg_return = np.mean(total_returns)
        median_return = np.median(total_returns)
        std_return = np.std(total_returns)
        max_return = max(total_returns)
        min_return = min(total_returns)
        positive_returns = len([r for r in total_returns if r > 0])

        print(f"总股票数量: {total_stocks}")
        print(f"成功回测股票数量: {successful_backtests}")
        print(f"平均收益率: {avg_return:.2%}")
        print(f"中位数收益率: {median_return:.2%}")
        print(f"收益率标准差: {std_return:.2%}")
        print(f"最大收益率: {max_return:.2%}")
        print(f"最小收益率: {min_return:.2%}")
        print(f"盈利股票数量: {positive_returns}")
        print(f"盈利比例: {positive_returns/successful_backtests:.2%}")

    def get_file_hash(self, file_path):
        """
        获取文件的哈希值，用于检测文件是否有变化

        Args:
            file_path: 文件路径

        Returns:
            str: 文件哈希值
        """
        try:
            stat = os.stat(file_path)
            # 使用文件大小和修改时间生成哈希
            content = f"{stat.st_size}_{stat.st_mtime}_{self.short_window}_{self.long_window}"
            return hashlib.md5(content.encode()).hexdigest()
        except:
            return None

    def save_single_stock_cache(self, stock_code, result, force_stop_check=None):
        """
        保存单个股票的缓存数据到独立文件

        Args:
            stock_code: 股票代码
            result: 股票测算结果
            force_stop_check: 强制停止检查函数（可选）
        """
        try:
            # 在开始保存前检查强制停止
            if force_stop_check and force_stop_check():
                return False  # 返回False表示被强制停止

            cache_file = os.path.join(self.cache_dir, f"{stock_code}.pkl")

            # 获取股票文件的哈希值
            stock_file_path = os.path.join(self.data_dir, f"{stock_code}.txt")
            file_hash = self.get_file_hash(stock_file_path)

            cache_data = {
                'result': result,
                'file_hash': file_hash,
                'parameters': {
                    'short_window': self.short_window,
                    'long_window': self.long_window,
                    'execution_price': self.execution_price
                },
                'cache_time': datetime.now().isoformat()
            }

            # 在写入前再次检查强制停止
            if force_stop_check and force_stop_check():
                return False  # 返回False表示被强制停止

            # 使用临时文件安全保存
            temp_file = cache_file + '.tmp'

            with open(temp_file, 'wb') as f:
                pickle.dump(cache_data, f)
                f.flush()
                os.fsync(f.fileno())

            # 验证临时文件完整性
            try:
                with open(temp_file, 'rb') as f:
                    test_data = pickle.load(f)
                if not isinstance(test_data, dict) or 'result' not in test_data:
                    raise ValueError("缓存数据验证失败")
            except Exception as e:
                os.remove(temp_file)
                raise Exception(f"缓存文件验证失败: {str(e)}")

            # 在最终替换前检查强制停止
            if force_stop_check and force_stop_check():
                # 清理临时文件
                try:
                    os.remove(temp_file)
                except:
                    pass
                return False  # 返回False表示被强制停止

            # 原子性替换
            if os.path.exists(cache_file):
                os.remove(cache_file)
            os.rename(temp_file, cache_file)

            print(f"💾 已保存股票缓存: {stock_code}")
            return True  # 返回True表示保存成功

        except Exception as e:
            print(f"❌ 保存股票缓存失败 {stock_code}: {str(e)}")
            # 清理临时文件
            temp_file = os.path.join(self.cache_dir, f"{stock_code}.pkl.tmp")
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False  # 返回False表示保存失败

    def load_single_stock_cache(self, stock_code):
        """
        加载单个股票的缓存数据

        Args:
            stock_code: 股票代码

        Returns:
            dict or None: 股票测算结果，如果缓存无效则返回None
        """
        try:
            cache_file = os.path.join(self.cache_dir, f"{stock_code}.pkl")

            if not os.path.exists(cache_file):
                return None

            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 验证缓存数据结构
            if not isinstance(cache_data, dict) or 'result' not in cache_data:
                print(f"⚠️ {stock_code}: 缓存数据格式错误")
                return None

            # 检查参数是否匹配
            cached_params = cache_data.get('parameters', {})
            if (cached_params.get('short_window') != self.short_window or
                cached_params.get('long_window') != self.long_window or
                cached_params.get('execution_price') != self.execution_price):
                print(f"⚠️ {stock_code}: 参数已变更，缓存无效")
                return None

            # 检查文件是否有变化
            stock_file_path = os.path.join(self.data_dir, f"{stock_code}.txt")
            if os.path.exists(stock_file_path):
                current_hash = self.get_file_hash(stock_file_path)
                cached_hash = cache_data.get('file_hash')

                if current_hash and cached_hash and current_hash != cached_hash:
                    print(f"⚠️ {stock_code}: 文件已变更，缓存无效")
                    return None

            return cache_data['result']

        except Exception as e:
            print(f"❌ 加载股票缓存失败 {stock_code}: {str(e)}")
            return None

    def load_individual_cache(self):
        """
        从单个股票缓存文件加载数据

        Returns:
            tuple: (已缓存的股票代码集合, 需要重新计算的股票文件列表)
        """
        print(f"📁 检查缓存目录: {self.cache_dir}")

        # 检查是否存在旧的缓存文件，如果存在则尝试迁移
        if os.path.exists(self.legacy_cache_file):
            print(f"🔄 发现旧格式缓存文件，尝试迁移到新格式...")
            self.migrate_legacy_cache()

        # 获取所有股票文件
        stock_files = glob.glob(os.path.join(self.data_dir, "*.txt"))
        cached_stocks = set()
        files_to_process = []

        print(f"📊 开始检查 {len(stock_files)} 只股票的缓存状态...")

        for file_path in stock_files:
            stock_code = os.path.basename(file_path).replace('.txt', '')

            # 尝试加载单个股票的缓存
            cached_result = self.load_single_stock_cache(stock_code)

            if cached_result is not None:
                # 缓存有效，加载到内存
                self.results[stock_code] = cached_result
                self.detailed_results[stock_code] = cached_result
                cached_stocks.add(stock_code)
                print(f"  ✓ {stock_code}: 使用缓存")
            else:
                # 缓存无效或不存在，需要重新计算
                files_to_process.append(file_path)
                print(f"  ⟳ {stock_code}: 需要计算")

        print(f"📊 缓存状态: {len(cached_stocks)} 只使用缓存, {len(files_to_process)} 只需要计算")
        return cached_stocks, files_to_process

    def migrate_legacy_cache(self):
        """
        将旧格式的缓存文件迁移到新的单个股票缓存格式
        """
        try:
            print(f"📦 开始迁移旧缓存文件: {os.path.basename(self.legacy_cache_file)}")

            with open(self.legacy_cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            if not isinstance(cache_data, dict):
                print("⚠️ 旧缓存文件格式不正确，跳过迁移")
                return

            # 获取旧缓存中的结果
            old_results = cache_data.get('results', {})
            old_detailed_results = cache_data.get('detailed_results', {})

            migrated_count = 0
            for stock_code, result in old_results.items():
                # 使用详细结果（如果有的话），否则使用普通结果
                stock_result = old_detailed_results.get(stock_code, result)

                # 保存为单个股票缓存
                self.save_single_stock_cache(stock_code, stock_result)
                migrated_count += 1

            print(f"✅ 迁移完成，共迁移 {migrated_count} 只股票的缓存")

            # 备份旧缓存文件
            backup_file = self.legacy_cache_file + '.backup'
            os.rename(self.legacy_cache_file, backup_file)
            print(f"📦 旧缓存文件已备份为: {os.path.basename(backup_file)}")

        except Exception as e:
            print(f"❌ 迁移旧缓存失败: {str(e)}")
            print("   将继续使用新的缓存机制")

    def save_cache(self):
        """
        安全保存缓存数据到文件 - 使用临时文件防止损坏
        """
        try:
            cache_data = {
                'results': self.results,
                'detailed_results': self.detailed_results,
                'file_hashes': {},
                'parameters': {
                    'short_window': self.short_window,
                    'long_window': self.long_window
                },
                'cache_time': datetime.now().isoformat()
            }

            # 计算所有股票文件的哈希值
            stock_files = glob.glob(os.path.join(self.data_dir, "*.txt"))
            for file_path in stock_files:
                stock_code = os.path.basename(file_path).replace('.txt', '')
                file_hash = self.get_file_hash(file_path)
                if file_hash:
                    cache_data['file_hashes'][stock_code] = file_hash

            # 使用临时文件安全保存
            temp_file = self.cache_file + '.tmp'

            # 先写入临时文件
            with open(temp_file, 'wb') as f:
                pickle.dump(cache_data, f)
                f.flush()  # 确保数据写入磁盘
                os.fsync(f.fileno())  # 强制同步到磁盘

            # 验证临时文件完整性
            try:
                with open(temp_file, 'rb') as f:
                    test_data = pickle.load(f)
                if not isinstance(test_data, dict) or 'results' not in test_data:
                    raise ValueError("缓存数据验证失败")
            except Exception as e:
                os.remove(temp_file)
                raise Exception(f"缓存文件验证失败: {str(e)}")

            # 原子性替换：删除旧文件，重命名临时文件
            if os.path.exists(self.cache_file):
                os.remove(self.cache_file)
            os.rename(temp_file, self.cache_file)

            print(f"\n✅ 缓存已安全保存: {self.cache_file}")
            print(f"   缓存了 {len(self.results)} 只股票的结果")

        except Exception as e:
            print(f"❌ 保存缓存失败: {str(e)}")
            # 清理临时文件
            temp_file = self.cache_file + '.tmp'
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass

    def load_cache(self):
        """
        从文件加载缓存数据

        Returns:
            tuple: (已缓存的股票代码集合, 需要重新计算的股票文件列表)
        """
        # 首先尝试加载带执行价格后缀的缓存文件
        if not os.path.exists(self.cache_file):
            # 如果没有找到带后缀的缓存文件，尝试加载旧格式的缓存文件（兼容性）
            old_cache_file = os.path.join(self.cache_dir, f"strategy_cache_MA{self.short_window}_{self.long_window}.pkl")
            if os.path.exists(old_cache_file) and self.execution_price == 'close':
                print(f"📖 找到旧格式缓存文件，将其用于收盘价执行: {os.path.basename(old_cache_file)}")
                self.cache_file = old_cache_file  # 临时使用旧缓存文件
            else:
                print("📝 未找到缓存文件，将进行完整计算")
                return set(), glob.glob(os.path.join(self.data_dir, "*.txt"))

        try:
            # 安全加载缓存文件
            with open(self.cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 验证缓存数据结构
            if not isinstance(cache_data, dict):
                print(f"⚠️ 缓存数据格式错误，重新计算")
                return set(), glob.glob(os.path.join(self.data_dir, "*.txt"))

            # 检查参数是否匹配
            cached_params = cache_data.get('parameters', {})
            if (cached_params.get('short_window') != self.short_window or
                cached_params.get('long_window') != self.long_window):
                print(f"⚠️  参数已变更 (MA{cached_params.get('short_window', '?')}-{cached_params.get('long_window', '?')} -> MA{self.short_window}-{self.long_window})，重新计算所有股票")
                return set(), glob.glob(os.path.join(self.data_dir, "*.txt"))

            # 恢复缓存的结果
            self.results = cache_data.get('results', {})
            self.detailed_results = cache_data.get('detailed_results', {})
            cached_hashes = cache_data.get('file_hashes', {})

            print(f"📖 加载缓存: {len(self.results)} 只股票")
            print(f"   缓存时间: {cache_data.get('cache_time', '未知')}")

            # 检查哪些文件需要重新计算
            stock_files = glob.glob(os.path.join(self.data_dir, "*.txt"))
            files_to_process = []
            cached_stocks = set()

            for file_path in stock_files:
                stock_code = os.path.basename(file_path).replace('.txt', '')
                current_hash = self.get_file_hash(file_path)
                cached_hash = cached_hashes.get(stock_code)

                if current_hash and cached_hash and current_hash == cached_hash:
                    # 文件未变化且有缓存结果
                    if stock_code in self.results:
                        cached_stocks.add(stock_code)
                        print(f"  ✓ {stock_code}: 使用缓存")
                    else:
                        files_to_process.append(file_path)
                        print(f"  ⟳ {stock_code}: 缓存中无结果，需重新计算")
                else:
                    # 文件有变化或无缓存
                    files_to_process.append(file_path)
                    if stock_code in self.results:
                        # 移除过期的缓存
                        del self.results[stock_code]
                        if stock_code in self.detailed_results:
                            del self.detailed_results[stock_code]
                        print(f"  ⟳ {stock_code}: 文件已变更，重新计算")
                    else:
                        print(f"  ⟳ {stock_code}: 新文件，需要计算")

            print(f"📊 缓存状态: {len(cached_stocks)} 只使用缓存, {len(files_to_process)} 只需要计算")
            return cached_stocks, files_to_process

        except EOFError:
            print(f"❌ 缓存文件损坏(EOFError): {os.path.basename(self.cache_file)}")
            print("   建议使用缓存修复工具删除损坏的文件")
            return set(), glob.glob(os.path.join(self.data_dir, "*.txt"))
        except pickle.UnpicklingError as e:
            print(f"❌ 缓存文件损坏(UnpicklingError): {os.path.basename(self.cache_file)} - {str(e)}")
            print("   建议使用缓存修复工具删除损坏的文件")
            return set(), glob.glob(os.path.join(self.data_dir, "*.txt"))
        except Exception as e:
            print(f"❌ 加载缓存失败: {str(e)}")
            return set(), glob.glob(os.path.join(self.data_dir, "*.txt"))

    def clear_cache(self):
        """
        清除缓存文件 - 支持新的单个股票缓存格式
        """
        try:
            cleared_count = 0

            # 清除新格式的单个股票缓存文件
            if os.path.exists(self.cache_dir):
                cache_files = glob.glob(os.path.join(self.cache_dir, "*.pkl"))
                for cache_file in cache_files:
                    os.remove(cache_file)
                    cleared_count += 1

                # 如果目录为空，删除目录
                if not os.listdir(self.cache_dir):
                    os.rmdir(self.cache_dir)
                    print(f"🗑️ 已删除空缓存目录: {self.cache_dir}")

            # 清除旧格式的缓存文件（如果存在）
            if os.path.exists(self.legacy_cache_file):
                os.remove(self.legacy_cache_file)
                cleared_count += 1
                print(f"🗑️ 已清除旧格式缓存文件: {os.path.basename(self.legacy_cache_file)}")

            if cleared_count > 0:
                print(f"🗑️ 缓存清除完成，共删除 {cleared_count} 个文件")
            else:
                print("📝 没有找到缓存文件")

        except Exception as e:
            print(f"❌ 清除缓存失败: {str(e)}")


def main():
    """主函数 - 演示时间对齐的平均收益率曲线"""
    print("双均线量化策略程序 - 时间对齐版")
    print("=" * 50)

    # 创建策略实例
    strategy = MovingAverageStrategyEnhanced(
        short_window=5,
        long_window=20,
        data_dir="../日线数据"
    )

    print("步骤1: 运行策略回测...")
    # 运行策略
    strategy.run_strategy(generate_charts=False)

    if not strategy.detailed_results:
        print("错误：没有成功的回测结果")
        return

    print(f"\n步骤2: 成功回测了{len(strategy.detailed_results)}只股票")
    print("步骤3: 正在生成时间对齐的平均收益率曲线...")

    # 生成时间对齐的平均收益率曲线
    selected_stocks, daily_avg = strategy.plot_average_return_curve(
        stock_count=50,
        save_charts=True
    )

    print("\n分析完成！")
    print("时间对齐修复要点:")
    print("1. 所有股票数据统一到相同的日期序列")
    print("2. 缺失日期使用前值填充")
    print("3. 确保每日样本数量一致")
    print("4. 添加数据覆盖率验证")
    print("5. 增加样本数量监控图表")


if __name__ == "__main__":
    main()