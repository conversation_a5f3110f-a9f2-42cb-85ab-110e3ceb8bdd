#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
支持优雅停止的策略工作进程 V5 - 新缓存机制版
使用文件标志而不是Event对象
支持新的单个股票实时缓存机制
"""

import multiprocessing as mp
import os
import glob
import time
from datetime import datetime

def strategy_worker_process_graceful(params):
    """
    支持优雅停止的策略工作进程
    使用文件标志检查停止信号，避免Event对象序列化问题
    """
    try:
        # 在工作进程中重新导入模块
        from 双均线策略_增强版 import MovingAverageStrategyEnhanced

        task_id = params.get('task_id', 'unknown')
        print(f"🔄 任务 {task_id} 开始执行...")

        # 创建停止标志文件路径
        graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"
        force_stop_flag_file = f"temp_force_stop_{task_id}.flag"

        def check_graceful_stop_signal():
            """检查优雅停止信号文件"""
            return os.path.exists(graceful_stop_flag_file)

        def check_force_stop_signal():
            """检查强制停止信号文件"""
            return os.path.exists(force_stop_flag_file)

        def cleanup_stop_flags():
            """清理停止标志文件"""
            try:
                if os.path.exists(graceful_stop_flag_file):
                    os.remove(graceful_stop_flag_file)
                if os.path.exists(force_stop_flag_file):
                    os.remove(force_stop_flag_file)
            except:
                pass

        # 创建策略实例
        use_enhanced_logic = params.get('use_enhanced_logic', True)

        if use_enhanced_logic:
            # 使用集成增强版策略
            from 双均线策略_集成增强版 import MovingAverageStrategyEnhanced as EnhancedStrategy
            strategy = EnhancedStrategy(
                short_window=params['short_window'],
                long_window=params['long_window'],
                data_dir=params['data_dir'],
                execution_price=params.get('execution_price', 'open')
            )
            print(f"🔧 使用增强版策略（包含停牌和涨跌停检测）")
        else:
            # 使用原版策略
            from 双均线策略_增强版 import MovingAverageStrategyEnhanced
            strategy = MovingAverageStrategyEnhanced(
                short_window=params['short_window'],
                long_window=params['long_window'],
                data_dir=params['data_dir'],
                execution_price=params.get('execution_price', 'open')
            )
            print(f"📊 使用标准版策略")

        execution_price_name = {
            'open': '开盘价',
            'close': '收盘价',
            'random': '随机价'
        }.get(strategy.execution_price, strategy.execution_price)

        print(f"开始运行双均线策略 (短期: {strategy.short_window}天, 长期: {strategy.long_window}天, 执行价格: {execution_price_name})")
        use_cache = params.get('use_cache', True)
        save_interval = params.get('save_interval', 20)

        if use_cache:
            print(f"💾 增量缓存：每处理 {save_interval} 只股票保存一次")
        else:
            print("🚫 缓存功能已禁用，将重新计算所有股票")
        print("=" * 60)

        # 运行前检查停止信号
        if check_graceful_stop_signal():
            cleanup_stop_flags()
            return {
                'success': False,
                'task_id': task_id,
                'error': "任务在启动前被优雅停止"
            }

        if check_force_stop_signal():
            cleanup_stop_flags()
            return {
                'success': False,
                'task_id': task_id,
                'error': "任务在启动前被强制停止"
            }

        # 加载缓存 - 使用新的单个股票缓存机制
        if use_cache:
            cached_stocks, files_to_process = strategy.load_individual_cache()
            total_cached = len(cached_stocks)
        else:
            files_to_process = glob.glob(os.path.join(strategy.data_dir, "*.txt"))
            total_cached = 0

        if not files_to_process and total_cached == 0:
            print(f"在目录 {strategy.data_dir} 中未找到股票数据文件")
            cleanup_stop_flags()
            return {
                'success': False,
                'task_id': task_id,
                'error': "没有找到股票数据文件"
            }

        # 加载后检查停止信号
        if check_graceful_stop_signal():
            cleanup_stop_flags()
            return {
                'success': False,
                'task_id': task_id,
                'error': "任务在加载数据后被优雅停止"
            }

        if check_force_stop_signal():
            cleanup_stop_flags()
            return {
                'success': False,
                'task_id': task_id,
                'error': "任务在加载数据后被强制停止"
            }

        total_returns = []
        successful_backtests = len(strategy.results)

        # 处理需要计算的文件
        if files_to_process:
            print(f"\n🔄 [任务ID: {task_id}] 开始处理 {len(files_to_process)} 只股票...")
            processed_count = 0

            for i, file_path in enumerate(files_to_process, 1):
                # 在开始处理每只股票前检查强制停止信号
                if check_force_stop_signal():
                    print(f"🛑 [任务ID: {task_id}] 收到强制停止信号，立即停止（已处理 {processed_count} 只股票）")
                    cleanup_stop_flags()
                    return {
                        'success': False,
                        'task_id': task_id,
                        'error': f"任务被强制停止，已处理 {processed_count} 只股票"
                    }

                # 在开始处理每只股票前检查优雅停止信号
                if check_graceful_stop_signal():
                    print(f"⏸️ [任务ID: {task_id}] 收到优雅停止信号，等待当前股票处理完成后停止")
                    # 继续处理当前股票，但这是最后一只

                stock_code = os.path.basename(file_path).replace('.txt', '')

                # 每100只股票显示一次进度
                if i % 100 == 0 or i <= 10:
                    print(f"[任务ID: {task_id}] 正在处理股票 ({i}/{len(files_to_process)}): {stock_code}")

                # 加载数据
                df = strategy.load_stock_data(file_path)
                if df is None or len(df) < strategy.long_window:
                    if i <= 10:
                        print(f"  [任务ID: {task_id}] 跳过 {stock_code}: 数据不足或加载失败")
                    # 如果收到优雅停止信号且当前股票无效，直接停止
                    if check_graceful_stop_signal():
                        print(f"⏸️ [任务ID: {task_id}] 优雅停止：当前股票无效，停止处理")
                        print(f"✅ [任务ID: {task_id}] 已实时保存 {processed_count} 只股票的计算结果")
                        cleanup_stop_flags()
                        return {
                            'success': False,
                            'task_id': task_id,
                            'error': f"任务被优雅停止，已处理 {processed_count} 只股票"
                        }
                    continue

                # 计算信号
                df = strategy.calculate_signals(df)

                # 回测
                result = strategy.backtest_strategy(df, stock_code)

                if result['trade_count'] > 0:
                    strategy.results[stock_code] = result
                    strategy.detailed_results[stock_code] = result
                    successful_backtests += 1
                    processed_count += 1

                    # 立即保存单个股票的缓存（新机制）
                    if use_cache:
                        strategy.save_single_stock_cache(stock_code, result)

                    if i <= 10 or i % 100 == 0:
                        print(f"  [任务ID: {task_id}] {stock_code}: 总收益率 {result['total_return']:.2%}, 交易次数 {result['trade_count']}")
                else:
                    if i <= 10:
                        print(f"  [任务ID: {task_id}] {stock_code}: 无交易信号")

                # 在当前股票处理完成后，检查是否收到优雅停止信号
                if check_graceful_stop_signal():
                    print(f"⏸️ [任务ID: {task_id}] 当前股票 {stock_code} 处理完成，响应优雅停止信号")
                    print(f"✅ [任务ID: {task_id}] 已实时保存 {processed_count} 只股票的计算结果")
                    cleanup_stop_flags()
                    return {
                        'success': False,
                        'task_id': task_id,
                        'error': f"任务被优雅停止，已处理 {processed_count} 只股票"
                    }

                # 显示进度信息（新缓存机制下无需批量保存）
                if processed_count % save_interval == 0:
                    print(f"\n📊 [任务ID: {task_id}] 进度更新: 已处理 {processed_count} 只股票")
                    print(f"  [任务ID: {task_id}] 当前进度: {i}/{len(files_to_process)} ({i/len(files_to_process)*100:.1f}%)")
                    print(f"  [任务ID: {task_id}] 已缓存股票数: {len(strategy.results)}")

            # 新缓存机制下，所有股票已实时保存，无需最终保存
            if processed_count > 0:
                print(f"\n✅ [任务ID: {task_id}] 处理完成，所有股票已实时缓存")
                print(f"   共缓存了 {len(strategy.results)} 只股票的结果")

                # 检查停止信号
                if check_graceful_stop_signal():
                    print(f"⏹️ [任务ID: {task_id}] 处理完成，响应优雅停止信号")
                    cleanup_stop_flags()
                    return {
                        'success': False,
                        'task_id': task_id,
                        'error': f"任务在完成时被优雅停止"
                    }

                if check_force_stop_signal():
                    print(f"🛑 [任务ID: {task_id}] 处理完成，响应强制停止信号")
                    cleanup_stop_flags()
                    return {
                        'success': False,
                        'task_id': task_id,
                        'error': f"任务在完成时被强制停止"
                    }
        else:
            print("✅ 所有股票都已缓存，无需重新计算")

        # 收集所有收益率
        for result in strategy.results.values():
            total_returns.append(result['total_return'])

        # 显示结果
        total_stocks = len(glob.glob(os.path.join(strategy.data_dir, "*.txt")))
        print(f"\n📈 处理完成:")
        print(f"   新计算: {len(files_to_process) if files_to_process else 0} 只股票")
        print(f"   使用缓存: {total_cached} 只股票")
        print(f"   总计: {total_stocks} 只股票")

        if total_returns:
            strategy.display_summary(total_returns, successful_backtests, total_stocks)

        # 计算结果统计
        if strategy.results:
            returns = [result['total_return'] for result in strategy.results.values()]
            avg_return = sum(returns) / len(returns)
            positive_count = len([r for r in returns if r > 0])
            win_rate = positive_count / len(returns) if len(returns) > 0 else 0
            max_return = max(returns) if returns else 0
            min_return = min(returns) if returns else 0

            # 构建包含执行价格信息的策略名称
            base_name = params.get('name', f"MA{params['short_window']}-MA{params['long_window']}")
            execution_price = params.get('execution_price', 'open')
            execution_suffix = {
                'open': '_open',
                'close': '_close',
                'random': '_rand'
            }.get(execution_price, '_open')

            strategy_name = base_name + execution_suffix

            print(f"✅ 任务 {task_id} 执行成功!")
            cleanup_stop_flag()

            return {
                'success': True,
                'task_id': task_id,
                'strategy_name': strategy_name,
                'params': params,
                'results': {
                    'strategy_name': strategy_name,
                    'stock_count': len(returns),
                    'avg_return': avg_return,
                    'win_rate': win_rate,
                    'max_return': max_return,
                    'min_return': min_return
                }
            }
        else:
            cleanup_stop_flag()
            return {
                'success': False,
                'task_id': task_id,
                'error': "没有计算结果"
            }

    except Exception as e:
        print(f"❌ 任务 {task_id} 执行异常: {str(e)}")
        import traceback
        traceback.print_exc()

        # 清理停止标志文件
        try:
            graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"
            force_stop_flag_file = f"temp_force_stop_{task_id}.flag"
            if os.path.exists(graceful_stop_flag_file):
                os.remove(graceful_stop_flag_file)
            if os.path.exists(force_stop_flag_file):
                os.remove(force_stop_flag_file)
        except:
            pass

        return {
            'success': False,
            'task_id': params.get('task_id', 'unknown'),
            'error': f"任务执行异常: {str(e)}"
        }