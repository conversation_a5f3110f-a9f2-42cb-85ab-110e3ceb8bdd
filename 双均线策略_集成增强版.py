#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双均线策略 - 集成增强交易逻辑版本
包含停牌检测和一字涨跌停处理功能
"""

import pandas as pd
import numpy as np
import os
import glob
import random
import hashlib
import pickle
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

class EnhancedTradingLogic:
    """增强的交易逻辑类 - 内置版本"""

    def __init__(self, limit_threshold=0.095):
        """
        初始化

        Args:
            limit_threshold: 涨跌停阈值，默认9.5%（考虑到浮点数精度）
        """
        self.limit_threshold = limit_threshold

    def is_suspended(self, df, index):
        """
        判断股票是否停牌

        Args:
            df: 股票数据DataFrame
            index: 数据索引

        Returns:
            bool: True表示停牌
        """
        row = df.iloc[index]

        # 停牌判断条件：
        # 1. 成交量为0
        # 2. 开盘价=最高价=最低价=收盘价（一字停牌）
        # 3. 价格为0或异常

        volume_zero = row.get('volume', 0) == 0

        # 检查是否一字停牌（所有价格相等）
        prices = [row['open'], row['high'], row['low'], row['close']]
        all_prices_equal = len(set(prices)) == 1

        # 检查价格是否异常
        price_invalid = any(p <= 0 for p in prices)

        return volume_zero or (all_prices_equal and volume_zero) or price_invalid

    def is_limit_up(self, df, index):
        """
        判断是否一字涨停

        Args:
            df: 股票数据DataFrame
            index: 数据索引

        Returns:
            bool: True表示一字涨停
        """
        if index == 0:
            return False

        current_row = df.iloc[index]
        prev_row = df.iloc[index - 1]

        # 计算涨幅
        price_change = (current_row['close'] - prev_row['close']) / prev_row['close']

        # 一字涨停判断：
        # 1. 涨幅接近涨停幅度（9.5%以上）
        # 2. 开盘价=最高价=最低价=收盘价
        # 3. 有成交量（区别于停牌）

        is_limit_price = price_change >= self.limit_threshold

        prices = [current_row['open'], current_row['high'], current_row['low'], current_row['close']]
        all_prices_equal = len(set([round(p, 2) for p in prices])) == 1  # 考虑浮点数精度

        has_volume = current_row.get('volume', 0) > 0

        return is_limit_price and all_prices_equal and has_volume

    def is_limit_down(self, df, index):
        """
        判断是否一字跌停

        Args:
            df: 股票数据DataFrame
            index: 数据索引

        Returns:
            bool: True表示一字跌停
        """
        if index == 0:
            return False

        current_row = df.iloc[index]
        prev_row = df.iloc[index - 1]

        # 计算跌幅
        price_change = (current_row['close'] - prev_row['close']) / prev_row['close']

        # 一字跌停判断：
        # 1. 跌幅接近跌停幅度（-9.5%以下）
        # 2. 开盘价=最高价=最低价=收盘价
        # 3. 有成交量（区别于停牌）

        is_limit_price = price_change <= -self.limit_threshold

        prices = [current_row['open'], current_row['high'], current_row['low'], current_row['close']]
        all_prices_equal = len(set([round(p, 2) for p in prices])) == 1  # 考虑浮点数精度

        has_volume = current_row.get('volume', 0) > 0

        return is_limit_price and all_prices_equal and has_volume

    def can_buy(self, df, index):
        """
        判断是否可以买入

        Args:
            df: 股票数据DataFrame
            index: 数据索引

        Returns:
            bool: True表示可以买入
        """
        # 不能买入的情况：
        # 1. 停牌
        # 2. 一字涨停

        if self.is_suspended(df, index):
            return False

        if self.is_limit_up(df, index):
            return False

        return True

    def can_sell(self, df, index):
        """
        判断是否可以卖出

        Args:
            df: 股票数据DataFrame
            index: 数据索引

        Returns:
            bool: True表示可以卖出
        """
        # 不能卖出的情况：
        # 1. 停牌
        # 2. 一字跌停

        if self.is_suspended(df, index):
            return False

        if self.is_limit_down(df, index):
            return False

        return True


class MovingAverageStrategyEnhanced:
    """
    增强版双均线策略类 - 集成版
    包含停牌检测和一字涨跌停处理功能
    """

    def __init__(self, short_window=5, long_window=20, data_dir="日线数据", execution_price='open'):
        """
        初始化策略参数

        Args:
            short_window: 短期均线周期
            long_window: 长期均线周期
            data_dir: 数据目录
            execution_price: 执行价格类型 ('open', 'close', 'random')
        """
        self.short_window = short_window
        self.long_window = long_window
        self.data_dir = data_dir
        self.execution_price = execution_price

        # 缓存相关 - 修改为每个测算创建独立目录
        self.cache_base_dir = "./cache"
        from 策略配置 import EXECUTION_PRICE_TYPES
        price_suffix = EXECUTION_PRICE_TYPES.get(execution_price, {}).get('suffix', execution_price)

        # 为每个策略参数组合创建独立的缓存目录
        strategy_name = f"MA{short_window}_{long_window}_{price_suffix}"
        self.cache_dir = os.path.join(self.cache_base_dir, strategy_name)
        os.makedirs(self.cache_dir, exist_ok=True)

        # 保留旧的缓存文件路径用于兼容性检查
        self.legacy_cache_file = os.path.join(self.cache_base_dir, f"strategy_cache_MA{short_window}_{long_window}_{execution_price}.pkl")

        self.detailed_results = {}
        self.results = {}  # 添加results属性以兼容工作进程

        # 初始化交易逻辑
        self.trading_logic = EnhancedTradingLogic()

        print(f"初始化策略: MA{short_window}-MA{long_window}, 执行价格: {execution_price}")

    def load_stock_data(self, file_path):
        """
        加载股票数据，支持多种格式

        Args:
            file_path: 股票数据文件路径

        Returns:
            DataFrame: 处理后的股票数据，如果失败返回None
        """
        try:
            # 尝试读取文件
            df = pd.read_csv(file_path, encoding='gbk')

            if df.empty:
                return None

            # 转换列名为小写并去除空格
            df.columns = [str(col).lower().strip() for col in df.columns]

            # 查找字段映射
            field_mapping = {}

            # 日期字段
            date_candidates = ['date', 'datetime', 'time', '日期', '时间']
            for candidate in date_candidates:
                if candidate in df.columns:
                    field_mapping['date'] = candidate
                    break

            # 价格字段
            price_fields = {
                'open': ['open', 'opening', '开盘', '开盘价'],
                'high': ['high', 'highest', '最高', '最高价'],
                'low': ['low', 'lowest', '最低', '最低价'],
                'close': ['close', 'closing', '收盘', '收盘价']
            }

            for field, candidates in price_fields.items():
                for candidate in candidates:
                    if candidate in df.columns:
                        field_mapping[field] = candidate
                        break

            # 成交量字段
            volume_candidates = ['volume', 'vol', '成交量', '量']
            for candidate in volume_candidates:
                if candidate in df.columns:
                    field_mapping['volume'] = candidate
                    break

            # 检查必需字段
            required_fields = ['date', 'open', 'high', 'low', 'close']
            missing_fields = [field for field in required_fields if field not in field_mapping]

            if missing_fields:
                print(f"缺少必需字段: {missing_fields}")
                return None

            # 重命名列
            rename_dict = {v: k for k, v in field_mapping.items()}
            df = df.rename(columns=rename_dict)

            # 转换数据类型
            try:
                df['date'] = pd.to_datetime(df['date'])
            except:
                print(f"日期转换失败: {file_path}")
                return None

            # 转换数值字段
            numeric_cols = ['open', 'high', 'low', 'close']
            if 'volume' in df.columns:
                numeric_cols.append('volume')

            for col in numeric_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 数据清洗 - 增强版
            # 1. 删除价格为0或负数的行
            price_cols = ['open', 'high', 'low', 'close']
            for col in price_cols:
                df = df[df[col] > 0]  # 只保留价格大于0的行

            # 2. 删除价格逻辑关系异常的行
            if all(col in df.columns for col in price_cols):
                # 最高价必须>=最低价
                df = df[df['high'] >= df['low']]
                # 开盘价和收盘价必须在最高最低价范围内
                df = df[(df['open'] >= df['low']) & (df['open'] <= df['high'])]
                df = df[(df['close'] >= df['low']) & (df['close'] <= df['high'])]

            # 3. 删除NaN值
            df = df.dropna()

            # 4. 检查数据量是否足够
            if len(df) < self.long_window * 2:  # 至少需要长期均线周期的2倍数据
                return None

            # 按日期排序
            df = df.sort_values('date').reset_index(drop=True)

            return df

        except Exception as e:
            print(f"加载文件 {file_path} 时出错: {str(e)[:50]}...")
            return None

    def calculate_signals(self, df):
        """
        计算移动平均线和交易信号

        Args:
            df: 股票数据DataFrame

        Returns:
            DataFrame: 包含信号的数据
        """
        # 计算移动平均线
        df[f'MA{self.short_window}'] = df['close'].rolling(window=self.short_window).mean()
        df[f'MA{self.long_window}'] = df['close'].rolling(window=self.long_window).mean()

        # 生成交易信号
        df['signal'] = 0
        df.loc[df[f'MA{self.short_window}'] > df[f'MA{self.long_window}'], 'signal'] = 1  # 买入信号
        df.loc[df[f'MA{self.short_window}'] < df[f'MA{self.long_window}'], 'signal'] = -1  # 卖出信号

        return df

    def get_execution_price(self, df, index):
        """
        根据执行价格类型获取执行价格

        Args:
            df: 股票数据DataFrame
            index: 数据索引

        Returns:
            float: 执行价格
        """
        if self.execution_price == 'close':
            return df.iloc[index]['close']
        elif self.execution_price == 'random':
            # 在最高价和最低价之间随机选择
            high = df.iloc[index]['high']
            low = df.iloc[index]['low']
            return random.uniform(low, high)
        else:
            # 默认使用开盘价
            return df.iloc[index]['open']

    def backtest_strategy(self, df, stock_code):
        """
        增强版回测策略 - 包含停牌和一字涨跌停处理

        Args:
            df: 包含信号的股票数据
            stock_code: 股票代码

        Returns:
            dict: 回测结果
        """
        position = 0  # 持仓状态：0=空仓，1=持有
        buy_price = 0
        trades = []
        total_return = 0

        # 创建收益率曲线数据
        equity_curve = []
        cumulative_return = 0

        # 延期交易队列
        pending_buy_signal = False
        pending_sell_signal = False

        # 统计信息
        delayed_buy_count = 0
        delayed_sell_count = 0

        for i in range(1, len(df)):
            current_date = df.iloc[i]['date']
            current_price = df.iloc[i]['close']
            signal = df.iloc[i-1]['signal']  # 使用前一天的信号，模拟第二天执行

            # 检查是否有新的交易信号
            if signal == 1 and position == 0:
                pending_buy_signal = True
            elif signal == -1 and position == 1:
                pending_sell_signal = True

            # 处理买入信号
            if pending_buy_signal and position == 0:
                if self.trading_logic.can_buy(df, i):
                    # 可以买入
                    position = 1
                    buy_price = self.get_execution_price(df, i)
                    pending_buy_signal = False

                    trades.append({
                        'date': current_date,
                        'action': 'buy',
                        'price': buy_price,
                        'return': 0,
                        'cumulative_return': cumulative_return,
                        'execution_type': self.execution_price,
                        'delay_reason': None
                    })

                else:
                    # 不能买入，记录延期
                    delayed_buy_count += 1
                    if self.trading_logic.is_suspended(df, i):
                        reason = "停牌"
                    elif self.trading_logic.is_limit_up(df, i):
                        reason = "一字涨停"
                    else:
                        reason = "其他"

            # 处理卖出信号
            if pending_sell_signal and position == 1:
                if self.trading_logic.can_sell(df, i):
                    # 可以卖出
                    position = 0
                    sell_price = self.get_execution_price(df, i)
                    pending_sell_signal = False

                    # 防止除零错误
                    if buy_price > 0:
                        trade_return = (sell_price - buy_price) / buy_price
                        total_return += trade_return
                        cumulative_return = total_return
                    else:
                        trade_return = 0
                        print(f"  警告: {stock_code} 买入价格为0，跳过该交易")

                    trades.append({
                        'date': current_date,
                        'action': 'sell',
                        'price': sell_price,
                        'return': trade_return,
                        'cumulative_return': cumulative_return,
                        'execution_type': self.execution_price,
                        'delay_reason': None
                    })

                else:
                    # 不能卖出，记录延期
                    delayed_sell_count += 1
                    if self.trading_logic.is_suspended(df, i):
                        reason = "停牌"
                    elif self.trading_logic.is_limit_down(df, i):
                        reason = "一字跌停"
                    else:
                        reason = "其他"

            # 记录每日收益率曲线数据
            if position == 1:
                # 持仓期间的浮动收益
                if buy_price > 0:
                    floating_return = (current_price - buy_price) / buy_price
                    current_cumulative = cumulative_return + floating_return
                else:
                    current_cumulative = cumulative_return
            else:
                # 空仓期间保持之前的累计收益
                current_cumulative = cumulative_return

            equity_curve.append({
                'date': current_date,
                'price': current_price,
                'position': position,
                'cumulative_return': current_cumulative,
                'ma_short': df.iloc[i][f'MA{self.short_window}'],
                'ma_long': df.iloc[i][f'MA{self.long_window}'],
                'signal': df.iloc[i]['signal'],
                'suspended': self.trading_logic.is_suspended(df, i),
                'limit_up': self.trading_logic.is_limit_up(df, i),
                'limit_down': self.trading_logic.is_limit_down(df, i)
            })

        # 如果最后还持有股票，尝试卖出
        if position == 1:
            final_index = len(df) - 1
            if self.trading_logic.can_sell(df, final_index):
                final_price = self.get_execution_price(df, final_index)
                # 防止除零错误
                if buy_price > 0:
                    final_return = (final_price - buy_price) / buy_price
                    total_return += final_return
                else:
                    final_return = 0
                    print(f"  警告: {stock_code} 最终卖出时买入价格为0")

                trades.append({
                    'date': df.iloc[-1]['date'],
                    'action': 'sell',
                    'price': final_price,
                    'return': final_return,
                    'cumulative_return': total_return,
                    'execution_type': self.execution_price,
                    'delay_reason': None
                })

        return {
            'stock_code': stock_code,
            'total_return': total_return,
            'trade_count': len([t for t in trades if t['action'] == 'sell']),
            'trades': trades,
            'equity_curve': equity_curve,
            'delayed_buy_count': delayed_buy_count,
            'delayed_sell_count': delayed_sell_count
        }

    def run_strategy(self, generate_charts=True, use_cache=True, save_interval=20):
        """
        运行策略，支持缓存和批量处理

        Args:
            generate_charts: 是否生成图表
            use_cache: 是否使用缓存
            save_interval: 保存间隔

        Returns:
            dict: 策略运行结果
        """
        print(f"🚀 开始运行增强版双均线策略 (MA{self.short_window}-MA{self.long_window})")
        print(f"📊 执行价格类型: {self.execution_price}")
        print(f"🔧 增强功能: 停牌检测、一字涨跌停处理")

        # 加载缓存 - 使用新的单个股票缓存机制
        if use_cache:
            cached_stocks, files_to_process = self.load_individual_cache()
            print(f"📁 已加载缓存，包含 {len(self.detailed_results)} 只股票的结果")
        else:
            # 不使用缓存，处理所有文件
            files_to_process = glob.glob(os.path.join(self.data_dir, "*.txt"))
            cached_stocks = set()

        print(f"📈 找到 {len(files_to_process)} 个需要处理的股票数据文件")

        total_returns = []
        successful_backtests = 0
        total_delayed_buys = 0
        total_delayed_sells = 0

        # 首先处理缓存中的结果
        if use_cache:
            for stock_code, result in self.detailed_results.items():
                total_returns.append(result['total_return'])
                successful_backtests += 1
                total_delayed_buys += result.get('delayed_buy_count', 0)
                total_delayed_sells += result.get('delayed_sell_count', 0)

        # 然后处理需要计算的文件
        for i, file_path in enumerate(files_to_process, 1):
            stock_code = os.path.basename(file_path).replace('.txt', '')

            # 加载和处理数据
            df = self.load_stock_data(file_path)
            if df is None:
                continue

            # 计算信号
            df_with_signals = self.calculate_signals(df)

            # 回测
            result = self.backtest_strategy(df_with_signals, stock_code)

            # 保存结果
            self.detailed_results[stock_code] = result
            self.results[stock_code] = result  # 同步到results属性
            total_returns.append(result['total_return'])
            successful_backtests += 1
            total_delayed_buys += result.get('delayed_buy_count', 0)
            total_delayed_sells += result.get('delayed_sell_count', 0)

            # 立即保存单个股票的缓存
            if use_cache:
                self.save_single_stock_cache(stock_code, result)

            # 显示进度信息
            if i % save_interval == 0:
                print(f"📊 进度更新: 已处理 {i}/{len(files_to_process)} 只股票")

        # 处理完成提示
        if use_cache and len(files_to_process) > 0:
            print(f"✅ 处理完成，所有股票已实时缓存")

        # 计算总股票数
        total_stocks = len(cached_stocks) + len(files_to_process)

        # 显示结果
        self.display_summary(total_returns, successful_backtests, total_stocks,
                           total_delayed_buys, total_delayed_sells)

        return {
            'total_returns': total_returns,
            'successful_backtests': successful_backtests,
            'total_stocks': total_stocks,
            'delayed_buys': total_delayed_buys,
            'delayed_sells': total_delayed_sells
        }

    def display_summary(self, total_returns, successful_backtests, total_stocks,
                       delayed_buys=0, delayed_sells=0):
        """
        显示策略运行结果摘要
        """
        if not total_returns:
            print("❌ 没有成功的回测结果")
            return

        avg_return = np.mean(total_returns)
        win_rate = len([r for r in total_returns if r > 0]) / len(total_returns)
        max_return = max(total_returns)
        min_return = min(total_returns)

        print(f"\n📊 策略运行结果摘要:")
        print(f"=" * 50)
        print(f"📈 策略参数: MA{self.short_window}-MA{self.long_window}")
        print(f"💰 执行价格: {self.execution_price}")
        print(f"📊 成功回测: {successful_backtests}/{total_stocks} 只股票")
        print(f"📈 平均收益率: {avg_return:.2%}")
        print(f"🎯 胜率: {win_rate:.1%}")
        print(f"🔝 最高收益: {max_return:.2%}")
        print(f"🔻 最低收益: {min_return:.2%}")
        print(f"⏸️ 延期买入次数: {delayed_buys}")
        print(f"⏸️ 延期卖出次数: {delayed_sells}")
        print(f"🔧 增强功能: 已启用停牌和涨跌停检测")

    def get_file_hash(self, file_path):
        """
        获取文件的哈希值，用于检测文件是否有变化
        """
        try:
            stat = os.stat(file_path)
            content = f"{stat.st_size}_{stat.st_mtime}_{self.short_window}_{self.long_window}"
            return hashlib.md5(content.encode()).hexdigest()
        except:
            return None

    def save_single_stock_cache(self, stock_code, result, force_stop_check=None):
        """
        保存单个股票的缓存数据到独立文件

        Args:
            stock_code: 股票代码
            result: 股票测算结果
            force_stop_check: 强制停止检查函数（可选）
        """
        try:
            # 在开始保存前检查强制停止
            if force_stop_check and force_stop_check():
                return False  # 返回False表示被强制停止

            cache_file = os.path.join(self.cache_dir, f"{stock_code}.pkl")

            # 获取股票文件的哈希值
            stock_file_path = os.path.join(self.data_dir, f"{stock_code}.txt")
            file_hash = self.get_file_hash(stock_file_path)

            cache_data = {
                'result': result,
                'file_hash': file_hash,
                'parameters': {
                    'short_window': self.short_window,
                    'long_window': self.long_window,
                    'execution_price': self.execution_price
                },
                'cache_time': datetime.now().isoformat()
            }

            # 在写入前再次检查强制停止
            if force_stop_check and force_stop_check():
                return False  # 返回False表示被强制停止

            # 使用临时文件安全保存
            temp_file = cache_file + '.tmp'

            with open(temp_file, 'wb') as f:
                pickle.dump(cache_data, f)
                f.flush()
                os.fsync(f.fileno())

            # 验证临时文件完整性
            try:
                with open(temp_file, 'rb') as f:
                    test_data = pickle.load(f)
                if not isinstance(test_data, dict) or 'result' not in test_data:
                    raise ValueError("缓存数据验证失败")
            except Exception as e:
                os.remove(temp_file)
                raise Exception(f"缓存文件验证失败: {str(e)}")

            # 在最终替换前检查强制停止
            if force_stop_check and force_stop_check():
                # 清理临时文件
                try:
                    os.remove(temp_file)
                except:
                    pass
                return False  # 返回False表示被强制停止

            # 原子性替换
            if os.path.exists(cache_file):
                os.remove(cache_file)
            os.rename(temp_file, cache_file)

            print(f"💾 已保存股票缓存: {stock_code}")
            return True  # 返回True表示保存成功

        except Exception as e:
            print(f"❌ 保存股票缓存失败 {stock_code}: {str(e)}")
            # 清理临时文件
            temp_file = os.path.join(self.cache_dir, f"{stock_code}.pkl.tmp")
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False  # 返回False表示保存失败

    def load_single_stock_cache(self, stock_code):
        """
        加载单个股票的缓存数据
        """
        try:
            cache_file = os.path.join(self.cache_dir, f"{stock_code}.pkl")

            if not os.path.exists(cache_file):
                return None

            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            # 验证缓存数据结构
            if not isinstance(cache_data, dict) or 'result' not in cache_data:
                print(f"⚠️ {stock_code}: 缓存数据格式错误")
                return None

            # 检查参数是否匹配
            cached_params = cache_data.get('parameters', {})
            if (cached_params.get('short_window') != self.short_window or
                cached_params.get('long_window') != self.long_window or
                cached_params.get('execution_price') != self.execution_price):
                print(f"⚠️ {stock_code}: 参数已变更，缓存无效")
                return None

            # 检查文件是否有变化
            stock_file_path = os.path.join(self.data_dir, f"{stock_code}.txt")
            if os.path.exists(stock_file_path):
                current_hash = self.get_file_hash(stock_file_path)
                cached_hash = cache_data.get('file_hash')

                if current_hash and cached_hash and current_hash != cached_hash:
                    print(f"⚠️ {stock_code}: 文件已变更，缓存无效")
                    return None

            return cache_data['result']

        except Exception as e:
            print(f"❌ 加载股票缓存失败 {stock_code}: {str(e)}")
            return None

    def load_individual_cache(self):
        """
        从单个股票缓存文件加载数据
        """
        print(f"📁 检查缓存目录: {self.cache_dir}")

        # 检查是否存在旧的缓存文件，如果存在则尝试迁移
        if os.path.exists(self.legacy_cache_file):
            print(f"🔄 发现旧格式缓存文件，尝试迁移到新格式...")
            self.migrate_legacy_cache()

        # 获取所有股票文件
        stock_files = glob.glob(os.path.join(self.data_dir, "*.txt"))
        cached_stocks = set()
        files_to_process = []

        print(f"📊 开始检查 {len(stock_files)} 只股票的缓存状态...")

        for file_path in stock_files:
            stock_code = os.path.basename(file_path).replace('.txt', '')

            # 尝试加载单个股票的缓存
            cached_result = self.load_single_stock_cache(stock_code)

            if cached_result is not None:
                # 缓存有效，加载到内存
                self.results[stock_code] = cached_result
                self.detailed_results[stock_code] = cached_result
                cached_stocks.add(stock_code)
                print(f"  ✓ {stock_code}: 使用缓存")
            else:
                # 缓存无效或不存在，需要重新计算
                files_to_process.append(file_path)
                print(f"  ⟳ {stock_code}: 需要计算")

        print(f"📊 缓存状态: {len(cached_stocks)} 只使用缓存, {len(files_to_process)} 只需要计算")
        return cached_stocks, files_to_process

    def migrate_legacy_cache(self):
        """
        将旧格式的缓存文件迁移到新的单个股票缓存格式
        """
        try:
            print(f"📦 开始迁移旧缓存文件: {os.path.basename(self.legacy_cache_file)}")

            with open(self.legacy_cache_file, 'rb') as f:
                cache_data = pickle.load(f)

            if isinstance(cache_data, dict):
                # 新格式：cache_data 直接是 detailed_results
                old_detailed_results = cache_data
            else:
                print("⚠️ 旧缓存文件格式不正确，跳过迁移")
                return

            migrated_count = 0
            for stock_code, result in old_detailed_results.items():
                # 保存为单个股票缓存
                self.save_single_stock_cache(stock_code, result)
                migrated_count += 1

            print(f"✅ 迁移完成，共迁移 {migrated_count} 只股票的缓存")

            # 备份旧缓存文件
            backup_file = self.legacy_cache_file + '.backup'
            os.rename(self.legacy_cache_file, backup_file)
            print(f"📦 旧缓存文件已备份为: {os.path.basename(backup_file)}")

        except Exception as e:
            print(f"❌ 迁移旧缓存失败: {str(e)}")
            print("   将继续使用新的缓存机制")

    def save_cache(self):
        """保存缓存到文件 - 保留用于兼容性"""
        print("ℹ️ 使用新的实时缓存机制，无需批量保存")

    def load_cache(self):
        """
        从文件加载缓存

        Returns:
            tuple: (已缓存的股票代码集合, 需要重新计算的股票文件列表)
        """
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'rb') as f:
                    self.detailed_results = pickle.load(f)
                print(f"📁 缓存已加载: {self.cache_file}")

                # 同步到results属性以兼容工作进程
                self.results = self.detailed_results.copy()

                # 获取已缓存的股票代码
                cached_stocks = set(self.detailed_results.keys())

                # 获取所有股票文件
                all_files = glob.glob(os.path.join(self.data_dir, "*.txt"))

                # 找出需要重新计算的文件
                files_to_process = []
                for file_path in all_files:
                    stock_code = os.path.basename(file_path).replace('.txt', '')
                    if stock_code not in cached_stocks:
                        files_to_process.append(file_path)

                print(f"📊 缓存状态: {len(cached_stocks)} 只使用缓存, {len(files_to_process)} 只需要计算")
                return cached_stocks, files_to_process

            else:
                print(f"📁 缓存文件不存在: {self.cache_file}")
                # 返回空缓存和所有文件
                all_files = glob.glob(os.path.join(self.data_dir, "*.txt"))
                return set(), all_files

        except Exception as e:
            print(f"❌ 加载缓存失败: {str(e)}")
            self.detailed_results = {}
            # 返回空缓存和所有文件
            all_files = glob.glob(os.path.join(self.data_dir, "*.txt"))
            return set(), all_files


def main():
    """主函数 - 演示增强版策略"""
    print("🚀 双均线策略 - 集成增强版")
    print("=" * 60)
    print("✅ 功能特性:")
    print("   • 停牌检测 - 成交量为0或价格异常时不交易")
    print("   • 一字涨停检测 - 涨幅≥9.5%且四价相等时不买入")
    print("   • 一字跌停检测 - 跌幅≤-9.5%且四价相等时不卖出")
    print("   • 延期交易 - 信号保持有效直到可以执行")
    print("   • 数据清洗 - 过滤异常价格数据")
    print("=" * 60)

    # 创建策略实例
    strategy = MovingAverageStrategyEnhanced(
        short_window=5,
        long_window=20,
        execution_price='close'
    )

    # 运行策略
    results = strategy.run_strategy(
        generate_charts=False,
        use_cache=True,
        save_interval=20
    )

    print(f"\n🎉 策略运行完成！")
    print(f"📊 处理了 {results['successful_backtests']} 只股票")
    print(f"⏸️ 总延期交易: 买入{results['delayed_buys']}次, 卖出{results['delayed_sells']}次")


if __name__ == "__main__":
    main()